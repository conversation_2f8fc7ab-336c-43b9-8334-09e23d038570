/* STARTING ROOT */

:root {
    /* BACKGROUND COLOR */
    --bg-white: #fff;
    --bg-grey: #f5f6f8;
    --bg-grey-lighter: #fafafc;
    --bg-blue: #1c165f;
    /* BORDER COLOR */
    --border-grey-light: 1px solid #ededf4;
    /* TEXT COLOR */
    --text-color-grey: #5e6065;
    --text-color-black-lighter: #32363b;
    --text-color-white: #fff;
    /* COLORS */
    --skyblue: #0b82b6;
}

/* ENDING ROOT */

/* STARTING FONT */

@font-face {
    font-family: "font-light";
    font-display: swap;
    src: url("../fonts/OpenSans-Medium.ttf")
}

@font-face {
    font-family: "font-regular";
    font-display: swap;
    src: url("../fonts/OpenSans-Regular.ttf");
}

@font-face {
    font-family: "font-medium";
    font-display: swap;
    src: url("../fonts/Roboto-Medium.ttf");
}

/* ENDING FONT */

/* STARTING ANIMATION */

/* ENDING ANIMATION */

/* STARTING DEFAULT CSS */

html, body {
    margin: 0;
    height: 100%;
    scroll-behavior: smooth;
}

html,
body {
  height: 100%;
}

body {
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

main {
    flex-grow: 1;
    overflow-x: hidden;
}

header, main, footer {
    flex-shrink: 0;
}

.font-light {
    font-family: "font-light" !important;
}

.font-regular {
    font-family: "font-regular" !important;
}

.font-medium {
    font-family: "font-medium" !important;
}

a {
    color: #0b5fdd;
}

cite {
    font-family: "font-light" !important;
    font-style: normal;
}

.bg-grey {
    background-color: var(--bg-grey) !important;
}

.bg-grey-lighter {
    background-color: var(--bg-grey-lighter) !important;
}

.bg-blue,
.bg-blue .header,
.bg-blue .carousel-item {
    background: #0a1486;
    background-image: url(http://themelooks.org/demo/bluishost/wp-content/uploads/2018/12/section_pattern-1.png);
    background-image: url(http://themelooks.org/demo/bluishost/wp-content/uploads/2018/12/section_pattern-1.png),-webkit-linear-gradient(top, #0a1486 0%,#050e7f 100%);
    background-image: url(http://themelooks.org/demo/bluishost/wp-content/uploads/2018/12/section_pattern-1.png),linear-gradient(to bottom, #090f50 0%,#05076b, #04094d 100%);
    background-attachment: fixed;
}

.bg-blue-dark,
.bg-blue-dark .carousel-item {
    background: #0816bf;
    background-image: url(http://themelooks.org/demo/bluishost/wp-content/uploads/2018/12/footer-pattern.png);
    background-image: -webkit-linear-gradient(top, #0b168a 0%,#050e7f 100%);
    background-image: url(http://themelooks.org/demo/bluishost/wp-content/uploads/2018/12/footer-pattern.png),linear-gradient(to bottom, #070d5f 0%,#040b6d, #070f6e 100%);
    background-attachment: fixed;
}

.bg-blue-dark:nth-child(3) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.233) !important;
}

.bg-blue *,
.bg-blue .carousel-item *,
.bg-blue-dark i,
.bg-blue-dark *,
.bg-blue-dark .carousel-item *,
.bg-blue-dark .icons li i {
    color: #fff !important;
}

.bg-blue .header {
    border-top: 1px solid rgba(255,255,255,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo {
    max-width: 200px;
}

.logo,
.logo img {
    width: 100%;
    height: auto;
}

.text-with-icon {
    text-decoration: none;
}

.text-with-icon,
.text-with-icon i {
    display: flex;
    align-items: center;
}

.text-black {
    color: black;
}

.text-grey {
    color: rgba(0, 0, 0, 0.76);
}

.text-grey-ligther {
    color: var(--text-color-black-lighter);
}

.text-link {
    text-decoration: none;
}

[class*="hover-border"] {
    border-bottom: 2.9px solid transparent;
    transition: border-bottom 0.2s ease-in-out;
}

.skyblue-hover-border:hover,
.menu-active {
    border-bottom: 2.9px solid #037dad !important;
}

.green-hover-border:hover {
    border-bottom: 2.9px solid #11a54f !important;
}

.green-hover {
    transition: color 0.1s ease-in-out;
}

.green-hover:hover {
    color: #11a54f !important;
}

.green-hover:active {
    color: #097c39 !important;
}

.skyblue-hover:hover {
    color: #037dad !important;
}

.skyblue-hover:active {
    color: #026891 !important;
}

.dropdown-menu[data-bs-popper] {
    left: unset !important;
    right: 0 !important;
}

.dropdown-menu {
    border: 1px solid rgb(0 0 0 / 9%) !important;
}

.dropdown-toggle::after {
    display: none;
}

.dropdown-menu span,
.dropdown-menu i {
    color: black !important;
}

.nav-link {
    color: black !important;
    border-bottom: 2px solid transparent;
    transition: border-bottom 0.1s ease-in-out;
}

.dropdown-toggle > i {
    font-size: 13px;
    transition: transform 0.1s ease-in-out;
}

.dropdown [aria-expanded*="true"] [class*="cci-arrow"] {
    transform: rotate(180deg);
}

.dropdown-large {
    padding: 20px;
    left: unset !important;
    right: 0;
}

.dropdown-large [class*="col-"] > span {
    margin-left: 5px;
}

.dropdown-large ul {
    padding: unset;
    margin-top: 6px;
}

.dropdown-large ul li a {
    border-radius: 3px;
}

.dropdown-large ul li {
    list-style-type: none;
}

header .dropdown-menu li:active i,
header .dropdown-menu li:active span {
    color: #fff !important;
}

.dropdown-large [class*="col"] {
    margin-top: 8px;
    margin-bottom: 8px;
}

.carousel-inner .carousel-item {
    min-height: 470px;
    border-bottom: var(--border-grey-light);
}
.carousel-inner .subtitle {
    font-weight: unset !important;
}

.carousel-inner p {
    font-size: 18px;
}

.carousel-inner .btn {
    max-width: 230px;
    font-size: 16px;
}

.content-img {
    width: 100%;
    max-width: 400px;
}

.content-img img {
    width: 100%;
    height: auto;
}

.box-register {
    width: 100%;
    max-width: 360px;
}

.reviews-stars i {
    background-color: #034ab4;
    color: #fff;
    padding: 4px;
    border-radius: 2px;
}

.btn-blue {
    background-color: #0063f8;
    color: #fff;
}
.btn-blue:hover{
    background-color: #0851bd;
    border-color: #0851bd;;
}

.btn-blue:active,
.btn-transparent:active {
    background-color: #053f96;
    border-color: #053f96;
}

.btn-transparent:hover,
.btn-outline-secondary:hover {
    background-color: #06658d !important;
    border-color: #06658d !important;
}

.btn-skyblue {
    background-color: #0a7cad;
    color: #fff !important;
}

.btn-skyblue:hover {
    background-color: #06658d;
}

.btn-skyblue:active {
    background-color: #044e6d;
}

.btn-pink {
    background-color: #d601a8;
}

.btn-pink:hover {
    background-color: rgb(168, 11, 142)
}

.btn-pink:active {
    background-color: rgb(138, 13, 117)
}

.btn-transparent {
    color: #fff;
    background-color: transparent;
    border-color: #fff;
}

.icons,
.links {
    margin: unset;
    padding: unset;
    display: flex;
}

.icons li {
    list-style-type: none;
    font-size: 22px;
    margin-right: 7px;
    transition: color 0.2s ease-in-out;
}

.icons li a {
    text-decoration: none;
}

.icons li i {
    color: var(--skyblue);
}

.icons li a,
.links li a {
    color: black;
    transition: color 0.1s ease-in-out;
    align-items: center;
}

.icons li a:hover,
.links li a:hover {
    color: #11a54f !important;
}

.icons li a:active,
.links li a:active {
    color: #097c39 !important;
}

.links {
    flex-direction: column;
    flex-wrap: wrap;
}

.links-in-line {
    flex-direction: row;
}

.links li {
    list-style-type: none;
    margin-top: 3.5px;
    margin-bottom: 3.5px;
    transition: color 0.1s ease-in-out;
}

.links li a {
    transition: color 0.1s ease-in-out;
}

.partners .owl-stage-outer {
    max-height: 40px;
}

.partners li {
    margin-right: 16px !important;
}

.payment-methods {
    flex-wrap: wrap;
}

.payment-methods li {
    font-size: 40px;
    pointer-events: none;
    height: 40px;
}

.menu-mobile-header {
    display: none;
}

[data-eventmenumobile] {
    display: none;
}

.overlay {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.219);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
}

.border-top-light {
    border-bottom: var(--border-grey-light) !important;
}

.border-bottom-light {
    border-bottom: var(--border-grey-light) !important;
}

.border-left-light {
    border-left: var(--border-grey-light) !important;
}

.border-right-light {
    border-right: var(--border-grey-light) !important;
}

.content-img {
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-img img {
    width: 100%;
    height: auto;
}

.content-img-icon {
    max-width: 60px;
    margin: 13px;
}

.img-reverse {
    transform: scaleX(-1);
}

.content-mu-img {
    width: 50px !important;
}

.content-feature {
    min-width: 215px;
    max-width: 500px;
}

.content-profile {
    max-width: 100px;
}

.features,
.user-in-line {
    margin: unset;
    display: flex;
    justify-content: center;
}

.features-transparent [class*="col-"] {
    background-color: transparent !important;
}

.features i {
    font-size: 70px;
    margin: 6px;
}

.features .text-left {
    margin: 6px;
}

.features .content-img {
    width: 160px;
    margin: 0 auto;
}

.features-hover-bg > [class*="col-"],
.card-hover-bg > .card,
.row-hover-bg > .col {
    transition: transform 0.2s ease-in-out;
}

.features-hover-bg > [class*="col-"]:hover,
.card-hover-bg > .card:hover,
.row-hover-bg > .col:hover {
    box-shadow: 0 6px 18px 0 rgba(#000, 0.1);
    transform: translateY(-6px);
}

.features *,
.card * {
    color: var(--color-blue-dark);
}

.features > [class*="col"] {
    width: 100%;
    max-width: 400px;
}

.tlds {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.tlds .tld {
    width: 110px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 10px;
}

.tlds .tld .content-img {
    width: 80px;
}

.tlds .tld cite {
    font-family: "font-bold";
    margin-top: 4px;
}

.domain {
    max-width: 900px;
}

.domain form [type*="submit"] {
    border-top-left-radius: unset;
    border-bottom-left-radius: unset;
}

.partners {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partners-slider {
    display: flex !important;
}

.partners img {
    max-width: 120px;
    margin-right: 18px;
    margin-top: 10px;
    margin-bottom: 10px;
    user-select: none;
}

.partners > img {
    height: max-content;
}

.carousel-indicators [data-bs-target] {
    background-color: rgb(3, 3, 88);
}

.carousel-indicators [data-bs-target]:hover {
    background-color: rgb(13, 13, 114) !important;
}

.social-network {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    padding: unset;
}

.social-network .follow-us {
    margin-right: 7px;
}

.social-network li {
    font-size: 20px;
    list-style-type: none;
    margin: 2.5px;
}

.social-network li a {
    color: #0a7cad;
    transition: color 0.2s ease-in-out;
    text-decoration: none;
}

.social-network li a:hover {
    color: #06658d;
}

.social-network li a:active {
    color: #044e6d;
}

table .content-img {
    max-width: 24px;
}

.fixed-top {
    position: fixed;
    top: -100%;
    right: 0;
    left: 0;
    z-index: 1030;
    transition: top 0.3s ease-in-out;
    border-bottom: var(--border-grey-light);
}

.up-to-top {
    min-width: 40px;
    padding: 10px;
    position: fixed;
    right: 50px;
    bottom: 70px;
    z-index: 1000;
    border-color: #0a7cad !important;
    background-color: #0a7cad !important;
    color: #fff !important;
}

.up-to-top:hover {
    background-color: #06658d !important;
}

.up-to-top:active {
    background-color: #044e6d !important;
}

.up-to-top i {
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(180deg);
}

.loading-page {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-blue);
    color: #fff;
    z-index: 1100;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-page .spinner-border {
    margin-right: 10px;
}

.loading-page .loading-title {
    margin: unset;
    font-size: 22px;
}

/* ENDING DEFAULT CSS */

/* STARTING HEADER AND FOOTER */

.header {
    background-color: var(--bg-white);
    border-top: var(--border-grey-light);
    border-bottom: var(--border-grey-light);
}

.header-top .text-with-icon {
    border-bottom: 2.9px solid transparent;
}

.header-info {
    background-image: linear-gradient(90deg, #0b82b6 0%, #0a74a1 100%);
    min-height: 143px;
    padding-top: 30px;
    padding-bottom: 30px;
    display: flex;
    align-items: center;
}

.header-info * {
    color: var(--text-color-white);
}

footer .col-md-12:nth-child(2) .col-md:nth-of-type(2) {
    margin-left: 160px;
}

footer .col-md-12:nth-child(3) {
    border-bottom: var(--border-grey-light);
}

footer .col-md-12:nth-child(3) .content-img {
    max-width: 120px;
}

footer .col-md-12:nth-child(3) p,
footer .col-md-12:nth-child(3) span,
footer .col-md-12:nth-child(4) p,
footer .col-md-12:nth-child(4) li {
    margin: unset !important;
}

footer .partners {
    justify-content: flex-start;
}

/* ENDING HEADER AND FOOTER */

/* STARTING PRICE */

.price,
.price-style-01 {
    width: 100%;
}

.price p,
.price span,
.one-price p,
.one-price span,
.one-price li {
    text-align: start;
}

.price ul {
    width: 100%;
    max-width: max-content;
    margin: 0 auto;
}

.price li {
    display: flex;
    align-items: center;
    font-size: 18px;
    margin-top: 7px;
    margin-bottom: 7px;
    text-align: start;
}

.price .content-img {
    width: 24px;
    margin-right: 6px;
}

.price-style-01 .card-header span,
.card-header span i {
    display: flex;
    justify-content: center;
    align-items: center;
}

.price-style-01 li {
    text-align: start;
}

.price .card-header .content-img {
    margin: unset;
    margin-left: 6px;
}

.price li i,
.price li strong,
.price span i,
.one-price li i {
    margin-right: 8px;
}

.price .text-muted {
    font-size: 20px;
}

.btn-outline-secondary {
    color: var(--text-color-black-lighter);
}

.price .btn-primary,
.btn-outline-secondary:hover {
    color: #fff !important;
    background-color: #0652DD;
    border-color: #0652DD;
}

.price .btn-primary:hover {
    background-color: #054bcc;
}

.price .btn-primary:active {
    background-color: #0343bb;
}

.price .card {
    margin: 7px;
}

.price .card-header {
    padding: 18px;
    background-color: transparent;
}

.price > [class*="col"] {
    padding: unset;
}

.one-price .card-deck {
    width: 80%;
}

.price-modern {
    padding: 30px;
    box-shadow: var(--box-shadow-one);
    display: flex;
    align-items: center;
}

.price-modern > [class*="col"],
.card-deck .card {
    padding: 10px;
}

.price-modern > [class*="col"] .card-header {
    margin-top: 20px;
}

.price-modern .card-body {
    padding-left: 24px !important;
    padding-right: 24px !important;
}

.price-modern ul {
    margin: unset;
}

.price-modern ul li {
    display: flex;
    align-items: center;
    margin-top: 7px;
    margin-bottom: 7px;
}

.price-modern ul li [class*="cci-check"] {
    margin-right: 6px;
    color: green;
}

.price-modern ul li [class*="cci-uncheck"] {
    margin-right: 6px;
    color: red;
}

.price-modern .btn {
    width: 100%;
    max-width: 300px;
    font-size: 16px;
    border-radius: 2px;
}

.price-modern .card-header {
    border: unset;
}

.price-ft {
    margin-top: 10px;
    margin-bottom: 10px !important;
}

/* ENDING PRICE */