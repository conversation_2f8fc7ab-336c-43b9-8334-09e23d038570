<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
<style type="text/css">
	.st0{fill:none;}
	.st1{font-family:'MyriadPro-Regular';}
	.st2{font-size:72px;}
</style>
<rect x="122" y="177" class="st0" width="297" height="216"/>
<text transform="matrix(1 0 0 1 122 228.1191)"><tspan x="0" y="0" class="st1 st2">Flag not </tspan><tspan x="0" y="86.4" class="st1 st2">available</tspan></text>
</svg>
